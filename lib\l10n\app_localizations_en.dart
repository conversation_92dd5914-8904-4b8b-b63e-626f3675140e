// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Bee Protect';

  @override
  String get homeTitle => 'Home';

  @override
  String get historyTitle => 'History';

  @override
  String get settingsTitle => 'Settings';

  @override
  String get startDetection => 'Start Detection';

  @override
  String get stopDetection => 'Stop Detection';

  @override
  String get detectedToday => 'Detected Today';

  @override
  String get alertDetected => 'Alert! Predator Detected';

  @override
  String get predatorDetectedMessage =>
      'A bee predator (Merops) has been detected in your area.';

  @override
  String get chartDayFilter => 'Day';

  @override
  String get chartWeekFilter => 'Week';

  @override
  String get chartMonthFilter => 'Month';

  @override
  String get chartYearFilter => 'Year';

  @override
  String get detectionHistory => 'Detection History';

  @override
  String get noDetectionsFound => 'No detections found';

  @override
  String get settingsAlertToggle => 'Enable Alerts';

  @override
  String get settingsLanguage => 'Language';

  @override
  String get settingsTheme => 'Dark Theme';

  @override
  String get languageEnglish => 'English';

  @override
  String get languageArabic => 'Arabic';

  @override
  String get languageFrench => 'French';

  @override
  String get detectionStatus => 'Detection Status';

  @override
  String get active => 'Active';

  @override
  String get inactive => 'Inactive';

  @override
  String get meropsPredator => 'Merops Predator';

  @override
  String get detected => 'Detected';

  @override
  String get notDetected => 'Not Detected';

  @override
  String get loading => 'Loading...';
}
