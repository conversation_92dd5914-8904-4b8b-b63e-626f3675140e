// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appTitle => 'Protection des Abeilles';

  @override
  String get homeTitle => 'Accueil';

  @override
  String get historyTitle => 'Historique';

  @override
  String get settingsTitle => 'Paramètres';

  @override
  String get startDetection => 'Démarrer la Détection';

  @override
  String get stopDetection => 'Arrêter la Détection';

  @override
  String get detectedToday => 'Détectés Aujourd\'hui';

  @override
  String get alertDetected => 'Alerte! Prédateur Détecté';

  @override
  String get predatorDetectedMessage =>
      'Un prédateur d\'abeilles (Guêpier) a été détecté dans votre région.';

  @override
  String get chartDayFilter => 'Jour';

  @override
  String get chartWeekFilter => 'Semaine';

  @override
  String get chartMonthFilter => 'Mois';

  @override
  String get chartYearFilter => 'Année';

  @override
  String get detectionHistory => 'Historique des Détections';

  @override
  String get noDetectionsFound => 'Aucune détection trouvée';

  @override
  String get settingsAlertToggle => 'Activer les Alertes';

  @override
  String get settingsLanguage => 'Langue';

  @override
  String get settingsTheme => 'Thème Sombre';

  @override
  String get languageEnglish => 'Anglais';

  @override
  String get languageArabic => 'Arabe';

  @override
  String get languageFrench => 'Français';

  @override
  String get detectionStatus => 'État de Détection';

  @override
  String get active => 'Actif';

  @override
  String get inactive => 'Inactif';

  @override
  String get meropsPredator => 'Prédateur Guêpier';

  @override
  String get detected => 'Détecté';

  @override
  String get notDetected => 'Non Détecté';

  @override
  String get loading => 'Chargement...';
}
