// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'حماية النحل';

  @override
  String get homeTitle => 'الرئيسية';

  @override
  String get historyTitle => 'السجل';

  @override
  String get settingsTitle => 'الإعدادات';

  @override
  String get startDetection => 'بدء الكشف';

  @override
  String get stopDetection => 'إيقاف الكشف';

  @override
  String get detectedToday => 'تم الكشف اليوم';

  @override
  String get alertDetected => 'تنبيه! تم رصد مفترس';

  @override
  String get predatorDetectedMessage =>
      'تم رصد مفترس النحل (الوروار) في منطقتك.';

  @override
  String get chartDayFilter => 'يوم';

  @override
  String get chartWeekFilter => 'أسبوع';

  @override
  String get chartMonthFilter => 'شهر';

  @override
  String get chartYearFilter => 'سنة';

  @override
  String get detectionHistory => 'سجل الكشف';

  @override
  String get noDetectionsFound => 'لم يتم العثور على أي كشف';

  @override
  String get settingsAlertToggle => 'تفعيل التنبيهات';

  @override
  String get settingsLanguage => 'اللغة';

  @override
  String get settingsTheme => 'المظهر الداكن';

  @override
  String get languageEnglish => 'الإنجليزية';

  @override
  String get languageArabic => 'العربية';

  @override
  String get languageFrench => 'الفرنسية';

  @override
  String get detectionStatus => 'حالة الكشف';

  @override
  String get active => 'نشط';

  @override
  String get inactive => 'غير نشط';

  @override
  String get meropsPredator => 'مفترس الوروار';

  @override
  String get detected => 'تم الكشف';

  @override
  String get notDetected => 'لم يتم الكشف';

  @override
  String get loading => 'جاري التحميل...';
}
